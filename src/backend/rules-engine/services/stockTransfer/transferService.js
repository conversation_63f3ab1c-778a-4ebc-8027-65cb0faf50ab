import fetch from 'node-fetch';
import Config from '../../config/Config.js';
import { getActiveConnection, invalidateConnection } from './authService.js';

const TRANSFER_PATH = '/account/transaction/transfer';

/**
 * Makes the Crediverse transfer call using a valid token obtained via getActiveConnection
 * authentication: { apiUsername, apiSecret, crediverseUsername, crediversePassword }
 */
export async function performStockTransfer(authentication, amount, targetMSISDN) {
  if (!authentication) throw new Error('Missing authentication');
  const tokenData = await getActiveConnection(authentication);

  const url = `${Config.ECDS_API_URL}:${Config.ECDS_API_PORT}${TRANSFER_PATH}`;

  const body = {
    amount,
    targetMSISDN,
  };

  const resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${tokenData.access_token}`,
    },
    body: JSON.stringify(body),
    // We rely on default timeouts; surrounding caller should enforce timeouts if needed
  });

  const text = await resp.text();
  let json;
  try {
    json = text ? JSON.parse(text) : null;
  } catch (err) {
    // Non-JSON body
    json = { raw: text };
  }

  if (!resp.ok) {
    // If 401/403, invalidate cached token so subsequent calls refresh
    if (resp.status === 401 || resp.status === 403) {
      invalidateConnection(authentication);
    }
    const err = new Error(`Crediverse transfer failed with status ${resp.status}`);
    err.status = resp.status;
    err.response = json;
    throw err;
  }

  return json;
}
