import fetch from 'node-fetch';
import Config from '../../config/Config.js';

/**
 * Lightweight token pool that mirrors the ConnectionManager/EndpointManager
 * approach used elsewhere. It requests an OAuth token from the ECDS API
 * and caches it until marginal expiry, refreshing as needed.
 *
 * This module intentionally follows the same lifecycle as the reference
 * implementation: create token, compute accessTokenMarginalExpiry and
 * refresh before expiry.
 */

const DEFAULT_REQUEST_TIMEOUT = 30000;
const REFRESH_BEFORE_EXPIRY_MS = 20000;

// In-memory cache keyed by a stable key derived from the auth params
const tokenCache = new Map();

function cacheKeyFromAuth(authentication) {
  // Use apiUsername + crediverseUsername to isolate different agent creds
  return `${authentication.apiUsername}::${authentication.crediverseUsername}`;
}

async function requestToken(authentication) {
  const url = `${Config.ECDS_API_URL}:${Config.ECDS_API_PORT}/oauth/token`;

  const body = new URLSearchParams({
    grant_type: 'password',
    username: authentication.crediverseUsername,
    password: authentication.crediversePassword,
  }).toString();

  const creds = Buffer.from(`${authentication.apiUsername}:${authentication.apiSecret}`).toString('base64');

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization: `Basic ${creds}`,
  };

  const controller = new AbortController();
  const timeout = authentication.timeout || DEFAULT_REQUEST_TIMEOUT;
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const resp = await fetch(url, {
      method: 'POST',
      headers,
      body,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!resp.ok) {
      const text = await resp.text();
      const err = new Error(`ECDS OAuth token request failed: ${resp.status} ${text}`);
      err.status = resp.status;
      throw err;
    }

    const data = await resp.json();

    if (!data.access_token || !data.expires_in) {
      const err = new Error('Invalid oauth response: missing access_token or expires_in');
      throw err;
    }

    const now = Date.now();
    const expires_in_ms = Number(data.expires_in) * 1000;
    data.accessTokenMarginalExpiry = new Date(now + expires_in_ms - REFRESH_BEFORE_EXPIRY_MS);

    // Store the time we fetched it for debugging
    data.fetchedAt = new Date(now);

    return data;
  } catch (err) {
    if (err.name === 'AbortError') {
      throw new Error(`ECDS OAuth token request timed out after ${timeout}ms`);
    }
    throw err;
  }
}

/**
 * Returns a valid tokenData object for the provided authentication parameters.
 * If a cached token exists and is still valid (marginal expiry in the future)
 * it is returned. Otherwise a new token is requested and cached.
 *
 * authentication: { apiUsername, apiSecret, crediverseUsername, crediversePassword, timeout? }
 */
export async function getActiveConnection(authentication) {
  if (!authentication || !authentication.apiUsername) {
    throw new Error('Missing authentication parameters for getActiveConnection');
  }

  const key = cacheKeyFromAuth(authentication);
  const cached = tokenCache.get(key);
  const now = new Date();

  if (cached && cached.accessTokenMarginalExpiry && new Date(cached.accessTokenMarginalExpiry) > now) {
    // token is still valid
    return cached;
  }

  // Otherwise request a new token and cache it
  const tokenData = await requestToken(authentication);
  tokenCache.set(key, tokenData);
  return tokenData;
}

export function invalidateConnection(authentication) {
  const key = cacheKeyFromAuth(authentication);
  tokenCache.delete(key);
}
