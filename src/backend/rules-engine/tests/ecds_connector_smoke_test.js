import { getEcdsApiToken, crediverseTransfer } from '../connectors/ecdsApiTransfer.js';

// Mock fetch to simulate the ECDS API endpoints
global.fetch = async (url, options) => {
  // token endpoint
  if (url.includes('/oauth/token')) {
    return {
      ok: true,
      status: 200,
      text: async () => JSON.stringify({ access_token: 'mock-token-123' }),
    };
  }

  // transfer endpoint
  if (url.includes('/transaction/transfer')) {
    // echo back the request body for inspection
    const body = options && options.body ? options.body : null;
    return {
      ok: true,
      status: 200,
      text: async () => JSON.stringify({ success: true, requestBody: body }),
    };
  }

  return { ok: false, status: 404, text: async () => 'not found' };
};

(async () => {
  try {
    const token = await getEcdsApiToken('ruleforge9', 'secret', 'crediverseUser', 'crediversePass');
    console.log('Obtained token:', token);

    const transferResp = await crediverseTransfer(token, 100, '27710000000');
    console.log('Transfer response:', transferResp);

    process.exit(0);
  } catch (err) {
    console.error('Smoke test failed:', err);
    process.exit(1);
  }
})();
