openapi: 3.0.0
info:
  title: Stock Transfer API
  description: |
    Internal Stock Transfer API used by RuleForge rules to perform airtime/credit transfers via the Crediverse/ECDS platform.
  version: 1.0.0
servers:
  - url: http://localhost:3000
paths:
  /api/stock-transfer:
    post:
      summary: Perform a stock/credit transfer to the given MSISDN
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  example: 100.5
                targetMSISDN:
                  type: string
                  example: "27710000000"
                authentication:
                  type: object
                  properties:
                    apiUsername:
                      type: string
                    apiSecret:
                      type: string
                    crediverseUsername:
                      type: string
                    crediversePassword:
                      type: string
              required:
                - amount
                - targetMSISDN
                - authentication
      responses:
        '200':
          description: Transfer accepted
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /api/stock-transfer/status/{transactionId}:
    get:
      summary: (Optional) Fetch status of a previously submitted transfer
      parameters:
        - in: path
          name: transactionId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Status returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  transactionId:
                    type: string
                  status:
                    type: string
        '404':
          description: Not found

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
