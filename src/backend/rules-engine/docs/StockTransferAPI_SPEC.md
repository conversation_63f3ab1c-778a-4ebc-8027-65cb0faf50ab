---
title: Stock Transfer API
classification: Confidential
created: 2025-10-03
updated: 2025-10-03
authors:
  - "[[Your Name]]"
version: 1.0.0
category: "[[Specifications]]"
tags:
  - specification
  - api
  - reference
  - stock-transfer

# Stock Transfer API

> Confidential: Internal API for RuleForge to perform Crediverse/ECDS transfers when rules trigger an apiCall.

## Purpose

This API exposes a small set of endpoints that allow RuleForge rules to request a stock/credit transfer to a target MSISDN using the Crediverse platform. It reuses the existing ECDS OAuth token acquisition flow.

## Scope

- One primary endpoint: POST /api/stock-transfer
- Optional status endpoint: GET /api/stock-transfer/status/{transactionId}
- Authentication: internal JWT (Bearer) for access to the RuleForge endpoint; backend calls to ECDS use OAuth tokens obtained via the existing flow.

## Authentication

Clients calling the RuleForge endpoint must add Authorization: Bearer <JWT_TOKEN> using the same internal JWT approach used elsewhere in RuleForge. The endpoint expects the ECDS authentication block inside the request body to acquire the Crediverse token.

## Endpoints

POST /api/stock-transfer
- Description: Perform a stock transfer to a target MSISDN.
- Request body:
  {
    "amount": number,
    "targetMSISDN": string,
    "authentication": {
      "apiUsername": string,
      "apiSecret": string,
      "crediverseUsername": string,
      "crediversePassword": string
    }
  }
- Responses:
  - 200: { success: true, data: { ...crediverseResponse } }
  - 400: Invalid request body
  - 401: Missing/invalid JWT
  - 500: Server error / upstream error

GET /api/stock-transfer/status/{transactionId}
- (Optional) Returns status information for a transfer. This is a simple pass-through and may be implemented later as needed.

## Example Request

POST /api/stock-transfer
Content-Type: application/json
Authorization: Bearer <internal-jwt>

{
  "amount": 100,
  "targetMSISDN": "27710000000",
  "authentication": {
    "apiUsername": "ruleforge9",
    "apiSecret": "762c05c5d90447c08cc2a467bc5a5ab2",
    "crediverseUsername": "ruleforge",
    "crediversePassword": "97aaa688"
  }
}

## Example Response

200 OK
{
  "success": true,
  "data": {
    "transactionId": "abc-123",
    "status": "ACCEPTED"
  }
}

## RuleForge apiCalls example

A rule can invoke the stock transfer API by adding an apiCalls entry in the ruleset. Example:

{
  "apiCalls": [
    {
      "name": "payAgentCommission",
      "type": "stockTransfer",
      "authentication": {
        "apiUsername": "ruleforge9",
        "apiSecret": "762c05c5d90447c08cc2a467bc5a5ab2",
        "crediverseUsername": "ruleforge",
        "crediversePassword": "97aaa688"
      },
      "parameters": [
        { "name": "amount", "type": "number", "value": "{salesCommission}" },
        { "name": "targetMSISDN", "type": "string", "value": "{sellerAgentMsisdn}" }
      ]
    }
  ]
}

## Best practices

- Do not include long-lived secrets in rulesets; prefer agent-level credentials managed by ops.
- Keep transfer timeouts small and implement retries on transient 5xx responses.

## Error handling

Returns standard HTTP codes. When Crediverse responds with an error, the API will return that status code and include the upstream payload in `details`.

## Approvals

| Role | Name | Date |
|------|------|------|

## Changelog

- 1.0.0 (2025-10-03) Initial version
