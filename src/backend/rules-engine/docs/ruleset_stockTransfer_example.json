{"schemaVersion": "2.4.0", "rulesetId": "AGENT_COMMISSION_TRANSFER_2025", "name": "Agent Commission Transfer", "rulesetVersion": 1, "status": "ACTIVE", "entities": [{"entityId": "CREDIVERSE", "transactionContexts": [{"contextId": "SALE_POST", "rules": [{"ruleId": "PayAgentCommission", "condition": {"type": "COMPARISON", "operator": ">=", "parameters": {"leftOperand": "{purchaseAmount}", "rightOperand": 0}}, "variableOperations": [{"variableId": "salesCommission", "operation": "SET", "value": "{purchaseAmount}"}, {"variableId": "salesCommission", "operation": "DIVIDE", "value": 10}], "apiCalls": [{"name": "payAgentCommission", "type": "stockTransfer", "authentication": {"apiUsername": "ruleforge9", "apiSecret": "762c05c5d90447c08cc2a467bc5a5ab2", "crediverseUsername": "ruleforge", "crediversePassword": "97aaa688"}, "parameters": [{"name": "amount", "type": "number", "value": "{salesCommission}"}, {"name": "targetMSISDN", "type": "string", "value": "{sellerAgentMsisdn}"}]}]}]}]}]}