import { performStockTransfer } from '../services/stockTransfer/transferService.js';
import { logger } from '../middleware/logger.js';

export default class StockTransferController {
  async transfer(req, res) {
    const { amount, targetMSISDN } = req.body;
    const auth = req.body.authentication || req.authentication || req.headers || {};

    try {
      // authentication details expected in body.authentication for internal calls from rules
      const authentication = req.body.authentication;

      const result = await performStockTransfer(authentication, amount, targetMSISDN);

      res.status(200).json({ success: true, data: result });
    } catch (err) {
      logger.warn([{ componentName: 'stockTransferController' }], err);
      const status = err.status || 500;
      res.status(status).json({ success: false, error: err.message, details: err.response || null });
    }
  }
}
