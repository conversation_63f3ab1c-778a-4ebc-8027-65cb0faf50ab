import { Router } from 'express';
import StockTransferController from '../controllers/stockTransferController.js';
import { joiValidationMiddleware } from '../utils/helper.js';
import Joi from 'joi';
import { apiTokenAuthMiddleware } from '../middleware/authMiddleware.js';

const transferSchema = Joi.object({
  amount: Joi.number().positive().required(),
  targetMSISDN: Joi.string().min(5).required(),
  authentication: Joi.object({
    apiUsername: Joi.string().required(),
    apiSecret: Joi.string().required(),
    crediverseUsername: Joi.string().required(),
    crediversePassword: Joi.string().required(),
  }).required(),
});

export default class StockTransferRoutes {
  constructor() {
    this.router = Router();
    this.controller = new StockTransferController();

    // Protected by API token — rules engine will call this internally using API token middleware
    this.router.post('/', apiTokenAuthMiddleware, joiValidationMiddleware(transferSchema), this.controller.transfer.bind(this.controller));
  }

  routes() {
    return this.router;
  }
}
